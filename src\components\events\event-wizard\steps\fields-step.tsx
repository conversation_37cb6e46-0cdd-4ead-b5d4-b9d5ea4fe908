'use client';

import { useState, useEffect } from 'react';
import { useWizard } from '@/components/events/event-wizard/wizard-container';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { X, Edit2, GripVertical, Plus, AlertCircle } from 'lucide-react';
import { z } from 'zod';
import { toast } from '@/components/ui/use-toast';
import { FieldType, EventField, SelectField } from '@/types/event-types';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';

// Type for field option
type FieldOption = string | { label: string; value: string };

// Field validation schema
const fieldSchema = z.object({
  id: z.string().min(1, 'Field ID is required'),
  type: z.nativeEnum(FieldType),
  label: z.string().min(1, 'Label is required'),
  description: z.string().optional(),
  required: z.boolean().optional(),
  placeholder: z.string().optional(),
  options: z.array(
    z.union([
      z.string(),
      z.object({ label: z.string(), value: z.string() })
    ])
  ).optional(),
  minLength: z.number().optional(),
  maxLength: z.number().optional(),
  min: z.number().optional(),
  max: z.number().optional(),
  step: z.number().optional(),
  content: z.string().optional(),
  defaultValue: z.any().optional(),
  order: z.number().optional(),
})
  // Add specific validation for different field types
  .superRefine((data, ctx) => {
    // For select, multiselect, and radio fields, options are required
    if ([FieldType.SELECT, FieldType.MULTISELECT, FieldType.RADIO].includes(data.type)) {
      if (!data.options || data.options.length === 0) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "At least one option is required for this field type",
          path: ["options"]
        });
      }
    }
  });

// Helper type guard
function isSelectField(field: EventField): field is SelectField {
  return field && [FieldType.SELECT, FieldType.MULTISELECT, FieldType.RADIO].includes(field.type);
}

// Helper to check if options array is string[] or object[]
function isStringOptions(options: SelectField['options']): options is string[] {
  return Array.isArray(options) && (options.length === 0 || typeof options[0] === 'string');
}

// Helper to check if options array is object[]
function isObjectOptions(options: SelectField['options']): options is { label: string; value: string }[] {
  return Array.isArray(options) && (options.length === 0 || (typeof options[0] === 'object' && options[0] !== null));
}

// Helper to safely get option display value
function getOptionDisplayValue(option: FieldOption): string {
  return typeof option === 'string' ? option : option.label;
}

export function FieldsStep() {
  const { formData, updateFormData, nextStep } = useWizard();
  const [fields, setFields] = useState<EventField[]>(formData.customFields || []);
  const [editingField, setEditingField] = useState<EventField | null>(null);
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // Add autofocus to appropriate field when editing
  useEffect(() => {
    if (editingField) {
      // Focus on field type select when adding/editing a field
      setTimeout(() => {
        const fieldTypeSelect = document.querySelector('[id*="fieldType"]') as HTMLElement;
        if (fieldTypeSelect) {
          fieldTypeSelect.focus();
        }
      }, 100);
    } else {
      // Focus on add new field button if no field is being edited
      const addButton = document.querySelector('button:has(svg[data-icon="plus"])') as HTMLElement;
      if (addButton) {
        addButton.focus();
      }
    }
  }, [editingField]);

  // Field type options for dropdown
  const fieldTypeOptions = [
    { value: FieldType.TEXT, label: 'Text' },
    { value: FieldType.EMAIL, label: 'Email' },
    { value: FieldType.NUMBER, label: 'Number' },
    { value: FieldType.PHONE, label: 'Phone' },
    { value: FieldType.SELECT, label: 'Select' },
    { value: FieldType.MULTISELECT, label: 'Multi-select' },
    { value: FieldType.CHECKBOX, label: 'Checkbox' },
    { value: FieldType.RADIO, label: 'Radio' },
    { value: FieldType.DATE, label: 'Date' },
    { value: FieldType.TIME, label: 'Time' },
    { value: FieldType.DATETIME, label: 'Date & Time' },
    { value: FieldType.HEADING, label: 'Heading' },
    { value: FieldType.PARAGRAPH, label: 'Paragraph' },
    { value: FieldType.DIVIDER, label: 'Divider' },
  ];

  // Default new field
  const defaultField: EventField = {
    id: `field-${Date.now()}`,
    type: FieldType.TEXT,
    label: '',
    description: '',
    required: false,
    order: fields.length,
  };

  // Start editing a new field
  const addNewField = () => {
    setEditingField({ ...defaultField, id: `field-${Date.now()}` });
    setEditingIndex(null);
    setValidationErrors({});
  };

  // Edit an existing field
  const editField = (index: number) => {
    if (index < 0 || index >= fields.length) {
      console.error('Invalid field index:', index);
      return;
    }

    const field = fields[index];
    if (!field) {
      console.error('Field not found at index:', index);
      return;
    }

    setEditingField({ ...field });
    setEditingIndex(index);
    setValidationErrors({});
  };

  // Delete a field
  const deleteField = (index: number) => {
    const updatedFields = [...fields];
    updatedFields.splice(index, 1);

    // Update order numbers
    const reorderedFields = updatedFields.map((field, idx) => ({
      ...field,
      order: idx,
    }));

    setFields(reorderedFields);
    updateFormData({ customFields: reorderedFields });
  };

  // Handle field type change
  const handleFieldTypeChange = (type: FieldType) => {
    const updatedField: any = {
      ...editingField,
      type,
    };

    // Reset type-specific properties
    delete updatedField.options;
    delete updatedField.placeholder;
    delete updatedField.minLength;
    delete updatedField.maxLength;
    delete updatedField.min;
    delete updatedField.max;
    delete updatedField.step;
    delete updatedField.content;
    delete updatedField.defaultValue;

    // Set type-specific default properties
    if ([FieldType.SELECT, FieldType.MULTISELECT, FieldType.RADIO].includes(type)) {
      updatedField.options = ['Option 1'];
    } else if (type === FieldType.CHECKBOX) {
      updatedField.defaultValue = false;
    } else if ([FieldType.TEXT, FieldType.EMAIL, FieldType.PHONE].includes(type)) {
      updatedField.placeholder = '';
    } else if (type === FieldType.NUMBER) {
      updatedField.min = undefined;
      updatedField.max = undefined;
      updatedField.step = 1;
    } else if ([FieldType.HEADING, FieldType.PARAGRAPH].includes(type)) {
      updatedField.content = type === FieldType.HEADING ? 'Section Heading' : 'Paragraph text goes here...';
    }

    setEditingField(updatedField);
  };

  // Handle input change for field form
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    setEditingField(prev => {
      if (!prev) return prev;
      return { ...prev, [name]: value };
    });

    // Clear validation error when field is edited
    if (validationErrors[name]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Handle number input change
  const handleNumberInputChange = (name: string) => (value: string) => {
    const numberValue = value === '' ? undefined : Number(value);

    setEditingField(prev => {
      if (!prev) return prev;
      return { ...prev, [name]: numberValue };
    });

    // Clear validation error when field is edited
    if (validationErrors[name]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Handle switch toggle
  const handleSwitchChange = (name: string) => (checked: boolean) => {
    setEditingField(prev => {
      if (!prev) return prev;
      return { ...prev, [name]: checked };
    });
  };

  // Handle option change for select/radio fields
  const handleOptionChange = (index: number, value: string) => {
    if (!editingField || !isSelectField(editingField)) return;

    if (isStringOptions(editingField.options)) {
      // Handle string options
      const newOptions = [...editingField.options];
      newOptions[index] = value;

      setEditingField({
        ...editingField,
        options: newOptions
      });
    } else if (isObjectOptions(editingField.options)) {
      // Handle object options
      const newOptions = [...editingField.options] as { label: string; value: string }[];
      newOptions[index] = {
        ...newOptions[index],
        label: value,
        value: value.toLowerCase().replace(/\s+/g, '-')
      };

      setEditingField({
        ...editingField,
        options: newOptions
      });
    }
  };

  // Add a new option for select/radio fields
  const addOption = () => {
    if (!editingField) return;

    if (isSelectField(editingField)) {
      // Handle existing SelectField
      if (isStringOptions(editingField.options)) {
        // For string options array
        const stringOptions = [...editingField.options];
        stringOptions.push(`Option ${stringOptions.length + 1}`);

        setEditingField({
          ...editingField,
          options: stringOptions
        });
      } else {
        // For object options array (isObjectOptions must be true here)
        // Create a copy that TypeScript can understand as the correct type
        const objectOptions = [...editingField.options].map(
          opt => typeof opt === 'object' ? opt : { label: opt, value: opt }
        ) as { label: string; value: string }[];

        objectOptions.push({
          label: `Option ${objectOptions.length + 1}`,
          value: `option-${objectOptions.length + 1}`
        });

        setEditingField({
          ...editingField,
          options: objectOptions
        });
      }
    } else if ([FieldType.SELECT, FieldType.MULTISELECT, FieldType.RADIO].includes(editingField.type)) {
      // Create a new SelectField with string array options
      const newField: SelectField = {
        ...editingField,
        type: editingField.type as FieldType.SELECT | FieldType.MULTISELECT | FieldType.RADIO,
        options: ['Option 1']
      };

      setEditingField(newField);
    }
  };

  // Remove an option for select/radio fields
  const removeOption = (index: number) => {
    if (!editingField || !isSelectField(editingField)) return;

    const newOptions = [...editingField.options];
    newOptions.splice(index, 1);

    setEditingField({
      ...editingField,
      options: newOptions
    });
  };

  // Save the current field
  const saveField = () => {
    try {
      if (!editingField) return;

      // Validate
      fieldSchema.parse(editingField);

      const updatedFields = [...fields];

      if (editingIndex !== null) {
        // Update existing field
        updatedFields[editingIndex] = editingField;
      } else {
        // Add new field
        updatedFields.push(editingField);
      }

      // Sort by order
      const sortedFields = updatedFields.sort((a, b) =>
        (a.order || 0) - (b.order || 0)
      );

      setFields(sortedFields);
      updateFormData({ customFields: sortedFields });

      // Reset form
      setEditingField(null);
      setEditingIndex(null);
      setValidationErrors({});
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors: Record<string, string> = {};
        error.errors.forEach(err => {
          if (err.path.length > 0) {
            if (err.path && err.path.length > 0 && err.path[0]) {
              errors[err.path[0]] = err.message;
            }
          }
        });
        setValidationErrors(errors);

        toast({
          title: "Validation Error",
          description: "Please fix the errors in the field form",
          variant: "destructive",
        });
      }
    }
  };

  // Cancel editing
  const cancelEditing = () => {
    setEditingField(null);
    setEditingIndex(null);
    setValidationErrors({});
  };

  // Handle drag and drop reordering
  const onDragEnd = (result: any) => {
    if (!result.destination) return;

    const items = [...fields];
    const [reorderedItem] = items.splice(result.source.index, 1);
    if (reorderedItem) {
      items.splice(result.destination.index, 0, reorderedItem);
    }

    // Update order property
    const updatedItems = items.map((item, index) => ({
      ...item,
      order: index,
    }));

    setFields(updatedItems);
    updateFormData({ customFields: updatedItems });
  };

  // Generate the field preview
  const getFieldPreview = (field: EventField) => {
    switch (field.type) {
      case FieldType.TEXT:
      case FieldType.EMAIL:
      case FieldType.PHONE:
        return (
          <Input
            type={field.type === FieldType.EMAIL ? 'email' : field.type === FieldType.PHONE ? 'tel' : 'text'}
            placeholder={field.placeholder || `Enter ${field.label}`}
            disabled
          />
        );
      case FieldType.NUMBER:
        return (
          <Input
            type="number"
            placeholder={field.placeholder || `Enter ${field.label}`}
            disabled
          />
        );
      case FieldType.SELECT:
        return (
          <Select disabled>
            <SelectTrigger>
              <SelectValue placeholder={field.placeholder || `Select ${field.label}`} />
            </SelectTrigger>
          </Select>
        );
      case FieldType.CHECKBOX:
        return (
          <div className="flex items-center space-x-2">
            <Checkbox disabled />
            <span className="text-sm">{field.label}</span>
          </div>
        );
      case FieldType.RADIO:
        return (
          <div className="space-y-1">
            {field.options && field.options.map((option, i) => (
              <div key={i} className="flex items-center space-x-2">
                <div className="h-4 w-4 rounded-full border border-gray-300" />
                <span className="text-sm">{typeof option === 'string' ? option : option.label}</span>
              </div>
            ))}
          </div>
        );
      case FieldType.DATE:
        return <Input type="date" disabled />;
      case FieldType.TIME:
        return <Input type="time" disabled />;
      case FieldType.DATETIME:
        return <Input type="datetime-local" disabled />;
      case FieldType.HEADING:
        return <h3 className="text-lg font-semibold">{field.content || field.label}</h3>;
      case FieldType.PARAGRAPH:
        return <p className="text-sm text-gray-500">{field.content || field.label}</p>;
      case FieldType.DIVIDER:
        return <div className="h-px bg-gray-200 w-full my-2" />;
      default:
        return <div>Field type not supported</div>;
    }
  };

  // Helper function to get options safely
  const getFieldOptions = (field: EventField) => {
    if (isSelectField(field)) {
      return field.options;
    }
    return [];
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-semibold mb-2">Custom Registration Fields</h2>
        <p className="text-gray-500">
          Define custom fields to collect information from participants
        </p>
      </div>

      {/* Field Form */}
      {editingField && (
        <Card>
          <CardHeader>
            <CardTitle>{editingIndex !== null ? 'Edit Field' : 'Add Field'}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="fieldType">Field Type</Label>
                  <Select
                    value={editingField.type}
                    onValueChange={(value) => handleFieldTypeChange(value as FieldType)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a field type" />
                    </SelectTrigger>
                    <SelectContent>
                      {fieldTypeOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {![FieldType.DIVIDER].includes(editingField.type) && (
                  <div className="space-y-2">
                    <Label htmlFor="label">
                      Label
                      <span className="text-red-500 ml-1">*</span>
                    </Label>
                    <Input
                      id="label"
                      name="label"
                      value={editingField.label}
                      onChange={handleInputChange}
                      placeholder="Field label"
                      className={validationErrors.label ? "border-red-500" : ""}
                    />
                    {validationErrors.label && (
                      <p className="text-sm text-red-500">{validationErrors.label}</p>
                    )}
                  </div>
                )}
              </div>

              {![FieldType.DIVIDER, FieldType.HEADING, FieldType.PARAGRAPH].includes(editingField.type) && (
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={editingField.description || ''}
                    onChange={handleInputChange}
                    placeholder="Help text for this field"
                    rows={2}
                  />
                </div>
              )}

              {/* Type-specific options */}
              {[FieldType.TEXT, FieldType.EMAIL, FieldType.PHONE, FieldType.NUMBER].includes(editingField.type) && (
                <div className="space-y-2">
                  <Label htmlFor="placeholder">Placeholder</Label>
                  <Input
                    id="placeholder"
                    name="placeholder"
                    value={(editingField as any).placeholder || ''}
                    onChange={handleInputChange}
                    placeholder="Placeholder text"
                  />
                </div>
              )}

              {[FieldType.TEXT, FieldType.EMAIL, FieldType.PHONE].includes(editingField.type) && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="minLength">Minimum Length</Label>
                    <Input
                      id="minLength"
                      name="minLength"
                      type="number"
                      value={(editingField as any).minLength?.toString() || ''}
                      onChange={handleInputChange}
                      placeholder="Minimum characters"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="maxLength">Maximum Length</Label>
                    <Input
                      id="maxLength"
                      name="maxLength"
                      type="number"
                      value={(editingField as any).maxLength?.toString() || ''}
                      onChange={handleInputChange}
                      placeholder="Maximum characters"
                    />
                  </div>
                </div>
              )}

              {editingField.type === FieldType.NUMBER && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="min">Minimum Value</Label>
                    <Input
                      id="min"
                      name="min"
                      type="number"
                      value={(editingField as any).min?.toString() || ''}
                      onChange={handleInputChange}
                      placeholder="Minimum value"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="max">Maximum Value</Label>
                    <Input
                      id="max"
                      name="max"
                      type="number"
                      value={(editingField as any).max?.toString() || ''}
                      onChange={handleInputChange}
                      placeholder="Maximum value"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="step">Step</Label>
                    <Input
                      id="step"
                      name="step"
                      type="number"
                      step="0.01"
                      value={(editingField as any).step?.toString() || '1'}
                      onChange={handleInputChange}
                      placeholder="Step value"
                    />
                  </div>
                </div>
              )}

              {[FieldType.SELECT, FieldType.MULTISELECT, FieldType.RADIO].includes(editingField.type) && (
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label>Options</Label>
                    <Button variant="ghost" size="sm" onClick={addOption} className="h-8 px-2 text-xs">
                      <Plus className="h-4 w-4 mr-1" />
                      Add Option
                    </Button>
                  </div>

                  <div className="space-y-2 border rounded-md p-3">
                    {isSelectField(editingField) && editingField.options.map((option, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <Input
                          value={getOptionDisplayValue(option)}
                          onChange={(e) => handleOptionChange(index, e.target.value)}
                          placeholder={`Option ${index + 1}`}
                          className="flex-1"
                        />
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={() => removeOption(index)}
                          className="h-8 w-8 text-red-500 hover:text-red-600"
                          disabled={isSelectField(editingField) && editingField.options.length <= 1}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}

                    {(!isSelectField(editingField) || editingField.options.length === 0) && (
                      <div className="text-center py-2 text-gray-500">
                        <AlertCircle className="h-4 w-4 mx-auto mb-1" />
                        <span className="text-xs">Add at least one option</span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {[FieldType.HEADING, FieldType.PARAGRAPH].includes(editingField.type) && (
                <div className="space-y-2">
                  <Label htmlFor="content">Content</Label>
                  <Textarea
                    id="content"
                    name="content"
                    value={(editingField as any).content || ''}
                    onChange={handleInputChange}
                    placeholder={editingField.type === FieldType.HEADING ? "Section heading text" : "Paragraph text"}
                    rows={editingField.type === FieldType.PARAGRAPH ? 4 : 2}
                  />
                </div>
              )}

              {![FieldType.HEADING, FieldType.PARAGRAPH, FieldType.DIVIDER].includes(editingField.type) && (
                <div className="flex items-center space-x-2">
                  <Switch
                    id="required"
                    checked={editingField.required || false}
                    onCheckedChange={handleSwitchChange('required')}
                  />
                  <Label htmlFor="required">This field is required</Label>
                </div>
              )}

              <div className="flex justify-end space-x-2 pt-4">
                <Button variant="outline" onClick={cancelEditing}>
                  Cancel
                </Button>
                <Button onClick={saveField}>
                  {editingIndex !== null ? 'Update Field' : 'Add Field'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Fields List */}
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">Form Fields ({fields.length})</h3>
          {!editingField && (
            <Button variant="outline" size="sm" onClick={addNewField}>
              <Plus className="h-4 w-4 mr-1" />
              Add Field
            </Button>
          )}
        </div>

        {fields.length > 0 ? (
          <DragDropContext onDragEnd={onDragEnd}>
            <Droppable droppableId="fields">
              {(provided) => (
                <div
                  {...provided.droppableProps}
                  ref={provided.innerRef}
                  className="space-y-3"
                >
                  {fields.map((field, index) => (
                    <Draggable key={field.id} draggableId={field.id} index={index}>
                      {(provided) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          className="relative"
                        >
                          <Card>
                            <CardContent className="p-4">
                              <div className="flex gap-4">
                                <div
                                  {...provided.dragHandleProps}
                                  className="flex items-center text-gray-400 hover:text-gray-600 cursor-grab"
                                >
                                  <GripVertical className="h-5 w-5" />
                                </div>

                                <div className="flex-1 space-y-1">
                                  <div className="flex justify-between items-start">
                                    <div>
                                      <div className="font-medium flex items-center gap-2">
                                        {field.label || '[No Label]'}
                                        {field.required && (
                                          <span className="text-red-500 text-xs">Required</span>
                                        )}
                                      </div>
                                      <div className="text-xs text-gray-500">
                                        {fieldTypeOptions.find(t => t.value === field.type)?.label || field.type}
                                      </div>
                                    </div>

                                    <div className="flex space-x-1">
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => editField(index)}
                                        className="h-8 w-8"
                                      >
                                        <Edit2 className="h-4 w-4" />
                                      </Button>
                                      <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={() => deleteField(index)}
                                        className="h-8 w-8 text-red-500 hover:text-red-600"
                                      >
                                        <X className="h-4 w-4" />
                                      </Button>
                                    </div>
                                  </div>

                                  {field.description && (
                                    <div className="text-xs text-gray-500 mb-2">
                                      {field.description}
                                    </div>
                                  )}

                                  <div>
                                    {getFieldPreview(field)}
                                  </div>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        </div>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
        ) : (
          <div className="border rounded-md p-8 text-center text-gray-500">
            <p>No custom fields defined yet. Add fields to collect specific information from participants.</p>
            <Button variant="outline" className="mt-4" onClick={addNewField}>
              <Plus className="h-4 w-4 mr-1" />
              Add First Field
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}