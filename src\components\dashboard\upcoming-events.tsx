'use client'

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { useEffect, useState } from "react"
import { format } from "date-fns"
import { CalendarIcon } from "lucide-react"
import { Event } from "@/repositories/event-repository"
import { useUpcomingEvents } from "@/hooks/use-events"

interface EventWithDetails extends Event {
  ticket_number?: string
  registration?: {
    ticket_number?: string
    status?: string
  }
}

interface UpcomingEventsProps {
  userId: string
  className?: string
}

export function UpcomingEvents({ userId: _, className }: UpcomingEventsProps) {
  const { events, loading, error } = useUpcomingEvents();
  const [processedEvents, setProcessedEvents] = useState<EventWithDetails[]>([]);

  useEffect(() => {
    // Add ticket numbers from registration data
    if (events && events.length > 0) {
      const eventsWithTickets = events.map(event => ({
        ...event,
        ticket_number: event.registration?.ticket_number
      }));
      setProcessedEvents(eventsWithTickets);
    } else {
      setProcessedEvents([]);
    }
  }, [events]);

  function formatEventTime(start: string | Date | null | undefined, end: string | Date | null | undefined): string {
    if (!start || !end) return 'Date not available';
    const startDate = new Date(start)
    const endDate = new Date(end)

    // Same day event
    if (startDate.toDateString() === endDate.toDateString()) {
      return `${format(startDate, 'EEEE, MMM d, yyyy')} • ${format(startDate, 'h:mm a')} - ${format(endDate, 'h:mm a')}`
    }

    // Multi-day event
    return `${format(startDate, 'MMM d')} - ${format(endDate, 'MMM d, yyyy')}`
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Upcoming Events</CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="space-y-2">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="h-24 bg-gray-100 animate-pulse rounded-md" />
            ))}
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <CalendarIcon className="h-12 w-12 text-gray-300 mb-4" />
            <h3 className="text-lg font-medium">Error loading events</h3>
            <p className="text-sm text-muted-foreground mt-1">
              {error.message || 'Failed to load upcoming events'}
            </p>
          </div>
        ) : processedEvents.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <CalendarIcon className="h-12 w-12 text-gray-300 mb-4" />
            <h3 className="text-lg font-medium">No upcoming events</h3>
            <p className="text-sm text-muted-foreground mt-1">
              You don&apos;t have any upcoming events registered.
            </p>
          </div>
        ) : (
          <div className="space-y-5">
            {processedEvents.map((event) => (
              <div key={event.id} className="group relative flex flex-col space-y-2">
                <div className="space-y-1">
                  <h3 className="font-medium leading-none">{event.title}</h3>
                  <p className="text-sm text-muted-foreground">
                    {formatEventTime(event.startDate, event.endDate)}
                  </p>
                </div>
                <div className="flex items-center text-sm">
                  {event.location && (
                    <span className="text-muted-foreground">📍 {event.location}</span>
                  )}
                  {event.ticket_number && (
                    <span className="ml-auto text-xs bg-gray-100 px-2 py-1 rounded-md">
                      #{event.ticket_number}
                    </span>
                  )}
                </div>
                <div className="absolute -left-2 top-2 h-full w-1 bg-primary rounded-full opacity-0 group-hover:opacity-100 transition-opacity" />
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}