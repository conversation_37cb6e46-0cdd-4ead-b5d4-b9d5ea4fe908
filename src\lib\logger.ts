/**
 * Simple logger utility for application logging
 */

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

class Logger {
  private prefix: string;

  constructor(prefix: string = '') {
    this.prefix = prefix ? `[${prefix}] ` : '';
  }

  debug(message: string, ...args: any[]): void {
    if (process.env.NODE_ENV === 'development') {
      console.debug(`${this.prefix}${message}`, ...args);
    }
  }

  info(message: string, ...args: any[]): void {
    console.info(`${this.prefix}${message}`, ...args);
  }

  warn(message: string, ...args: any[]): void {
    console.warn(`${this.prefix}${message}`, ...args);
  }

  error(message: string, ...args: any[]): void {
    console.error(`${this.prefix}${message}`, ...args);
  }
}

export const logger = new Logger('App'); 